import React from "react";
import { render, screen } from "@testing-library/react";
import DeleteAccount from "../DeleteAccount";

jest.mock("next-intl", () => ({
  useLocale: () => "en",
  useTranslations: () => (key: string) => key,
  useRouter: () => ({
    push: jest.fn(),
  }),
}));

jest.mock("next-auth/react", () => ({
  useSession: () => ({
    data: { user: { name: "Test User" } },
    status: "authenticated",
  }),
  signOut: jest.fn(),
}));

describe("Delete Account", () => {
  it("renders all required components", () => {
    render(<DeleteAccount />);

    // Check if title is rendered
    expect(screen.getByText("title")).toBeInTheDocument();

    // Check if all child components are rendered
    expect(screen.getByTestId("word-form")).toBeInTheDocument();
    expect(screen.getByTestId("word-list")).toBeInTheDocument();
    expect(screen.getByTestId("ai-generate")).toBeInTheDocument();
  });
});
