type QuestionAiIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const QuestionAiIcon: React.FC<QuestionAiIconProps> = ({ className, ...props }) => {
  return (
    <svg width="80" height="81" viewBox="0 0 80 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M28.1826 14.3575C30.586 7.32429 40.3039 7.11162 43.1533 13.7188L43.3945 14.3614L46.6377 23.8457C47.381 26.0209 48.5822 28.0121 50.1602 29.6836C51.7381 31.3551 53.6564 32.669 55.7852 33.5362L56.6572 33.8614L66.1416 37.1006C73.1748 39.504 73.3873 49.2219 66.7842 52.0713L66.1416 52.3125L56.6572 55.5557C54.4814 56.2985 52.4896 57.4993 50.8174 59.0772C49.1451 60.6552 47.8306 62.574 46.9629 64.7032L46.6377 65.5713L43.3984 75.0596C40.9951 82.0927 31.2772 82.3061 28.4316 75.7032L28.1826 75.0596L24.9434 65.5752C24.2006 63.3993 22.9998 61.4076 21.4219 59.7354C19.8439 58.0631 17.9251 56.7486 15.7959 55.8809L14.9277 55.5557L5.44336 52.3165C-1.59377 49.9131 -1.80716 40.1952 4.79981 37.3497L5.44336 37.1006L14.9277 33.8614C17.1029 33.1181 19.0941 31.9169 20.7656 30.3389C22.437 28.761 23.75 26.8426 24.6172 24.7139L24.9434 23.8457L28.1826 14.3575ZM35.4258 54.0791C34.479 54.0792 33.6693 54.4115 32.9971 55.0743C32.3344 55.737 32.0072 56.5322 32.0166 57.46C32.0071 58.3975 32.3342 59.2027 32.9971 59.875C33.6693 60.5378 34.479 60.8691 35.4258 60.8692C36.0318 60.8692 36.5912 60.7181 37.1025 60.4151C37.6139 60.1026 38.0254 59.6901 38.3379 59.1788C38.6598 58.658 38.8255 58.085 38.835 57.46C38.8254 56.5321 38.4796 55.7371 37.7979 55.0743C37.116 54.4114 36.3254 54.0791 35.4258 54.0791ZM36.0088 31.0118C34.2948 31.0118 32.7271 31.3191 31.3066 31.9346C29.8864 32.5406 28.7455 33.4639 27.8838 34.7041C27.0221 35.9351 26.5669 37.4979 26.5195 39.3916H32.3154C32.3439 38.6154 32.5335 37.9671 32.8838 37.4463C33.2342 36.916 33.6842 36.5181 34.2334 36.253C34.7825 35.9785 35.3651 35.8409 35.9805 35.8409C36.6147 35.8409 37.1922 35.9733 37.7129 36.2383C38.2432 36.5035 38.665 36.8826 38.9775 37.375C39.29 37.8674 39.4463 38.4404 39.4463 39.0938C39.4463 39.7092 39.3087 40.2678 39.0342 40.7696C38.7596 41.262 38.3898 41.712 37.9258 42.1192C37.4619 42.5263 36.9366 42.9192 36.3496 43.2979C35.5636 43.7903 34.8907 44.3403 34.332 44.9463C33.7735 45.5523 33.3472 46.3475 33.0537 47.3321C32.7696 48.3169 32.6228 49.6387 32.6133 51.2959V51.7784H37.9971V51.2959C38.0065 50.3681 38.1253 49.5866 38.3525 48.9522C38.5893 48.3177 38.9445 47.7584 39.418 47.2754C39.9009 46.7926 40.5167 46.3288 41.2646 45.8838C42.1545 45.3537 42.9258 44.7616 43.5791 44.1084C44.2325 43.4456 44.7398 42.6873 45.0996 41.835C45.4688 40.9734 45.6533 39.9887 45.6533 38.8809C45.6533 37.2239 45.2417 35.808 44.418 34.6338C43.5942 33.4597 42.4575 32.5648 41.0088 31.9493C39.56 31.3243 37.8931 31.0118 36.0088 31.0118Z"
        fill="currentColor"
      />
      <path
        d="M67.9424 0.500047C68.694 0.500103 69.4306 0.710818 70.0684 1.10844C70.7063 1.5063 71.22 2.07585 71.5508 2.75102L71.7441 3.22075L73.1504 7.34477L77.2783 8.75102C78.0316 9.00699 78.692 9.48077 79.1758 10.1124C79.6596 10.7441 79.9453 11.5057 79.9961 12.2999C80.0469 13.094 79.8603 13.885 79.4609 14.5733C79.0615 15.2616 78.4667 15.8162 77.752 16.1661L77.2783 16.3584L73.1543 17.7657L71.748 21.8926C71.4916 22.6458 71.0177 23.3066 70.3857 23.7901C69.7538 24.2735 68.9922 24.558 68.1982 24.6084C67.4041 24.6588 66.6128 24.472 65.9248 24.0723C65.2368 23.6726 64.6826 23.0781 64.333 22.3633L64.1406 21.8926L62.7334 17.7696L58.6064 16.3633C57.853 16.1073 57.1919 15.6328 56.708 15.001C56.2243 14.3694 55.9386 13.6084 55.8877 12.8145C55.8369 12.0203 56.0234 11.2284 56.4229 10.5401C56.8223 9.85191 57.4172 9.29816 58.1318 8.94829L58.6064 8.75493L62.7295 7.34868L64.1357 3.22075C64.4067 2.42675 64.9201 1.73798 65.6025 1.25005C66.2851 0.762105 67.1034 0.499542 67.9424 0.500047Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default QuestionAiIcon;
