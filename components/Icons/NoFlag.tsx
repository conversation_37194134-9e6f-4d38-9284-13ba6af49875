type NoFlagProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const NoFlag: React.FC<NoFlagProps> = ({ className, ...props }) => (
  <svg width="111" height="81" viewBox="0 0 111 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
    <path
      d="M111 67.8032C111 71.0674 109.701 74.1979 107.388 76.5061C105.075 78.8142 101.938 80.1109 98.6667 80.1109H12.3333C9.06233 80.1109 5.9253 78.8142 3.61235 76.5061C1.2994 74.1979 0 71.0674 0 67.8032V12.4186C0 9.15439 1.2994 6.02388 3.61235 3.71574C5.9253 1.4076 9.06233 0.110897 12.3333 0.110897H98.6667C101.938 0.110897 105.075 1.4076 107.388 3.71574C109.701 6.02388 111 9.15439 111 12.4186V67.8032Z"
      fill="#4DD0E1"
    />
    <path
      d="M65.0759 33.8801L55.1518 58.1109H59.1587L61.236 53.2311H68.9158L70.993 58.1109H75L65.0759 33.8801ZM62.812 49.5292L65.0759 42.6175L67.3398 49.5292H62.812ZM57.3571 45.9955C57.3359 45.9795 55.6107 44.6687 53.5046 42.4055C56.8618 37.8925 58.7635 32.7628 59.5396 30.3465H64.1429V26.6446H52.4375V23.1109H48.7054V26.6446H37V30.3465H55.5971C54.7896 32.6139 53.3027 36.1938 51.0346 39.4633C48.2626 35.8085 47.0361 33.0792 47.02 33.0506L46.4152 31.8609L43.192 33.7119L43.7747 34.878C43.8502 35.0092 45.2328 38.0667 48.4153 42.1615C48.4933 42.2633 48.5722 42.3626 48.6511 42.4619C44.4337 47.2458 41.0892 49.1161 41.046 49.148L39.7143 50.034L41.6652 53.0628L43.3022 52.0978C43.4888 51.9573 46.8079 50.0786 51.1058 45.3014C53.1856 47.5125 54.7718 48.7366 54.8634 48.8073L55.9152 49.5292L57.3571 45.9955Z"
      fill="#1E272E"
    />
  </svg>
);

export default NoFlag;
