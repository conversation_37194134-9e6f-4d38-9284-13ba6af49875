type StatsIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const StatsIcon: React.FC<StatsIconProps> = ({ className, ...props }) => (
  <svg width="80" height="81" viewBox="0 0 80 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
    <path
      opacity="0.8"
      d="M71.1111 0.5H8.88889C6.53141 0.5 4.27049 1.4365 2.60349 3.10349C0.936505 4.77049 0 7.03141 0 9.38889V71.6111C0 73.9686 0.936505 76.2295 2.60349 77.8965C4.27049 79.5635 6.53141 80.5 8.88889 80.5H71.1111C73.4686 80.5 75.7295 79.5635 77.3965 77.8965C79.0635 76.2295 80 73.9686 80 71.6111V9.38889C80 7.03141 79.0635 4.77049 77.3965 3.10349C75.7295 1.4365 73.4686 0.5 71.1111 0.5ZM71.1111 71.6111H8.88889V9.38889H71.1111V71.6111ZM26.6667 62.7222H17.7778V40.5H26.6667V62.7222ZM44.4444 62.7222H35.5556V18.2778H44.4444V62.7222ZM62.2222 62.7222H53.3333V31.6111H62.2222V62.7222Z"
      fill="currentColor"
    />
  </svg>
);

export default StatsIcon;
