type TextIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const TextIcon: React.FC<TextIconProps> = ({ className, ...props }) => {
  return (
    <svg width="83" height="81" viewBox="0 0 83 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
      <path
        d="M38.4776 42.2141H19.9167L29.1951 15.6423L38.4776 42.2141ZM41.3745 50.5114L42.732 54.3903L49.2252 47.877L33.6481 3.27525C32.1706 -0.943888 26.2195 -0.943888 24.7504 3.27525L4.592 60.97C4.41239 61.4843 4.33558 62.029 4.36596 62.5731C4.39635 63.1171 4.53334 63.6499 4.7691 64.1409C5.00486 64.6318 5.33478 65.0715 5.74003 65.4346C6.14527 65.7978 6.61791 66.0774 7.13094 66.2574C7.64398 66.4375 8.18737 66.5145 8.73008 66.484C9.2728 66.4536 9.80422 66.3162 10.294 66.0799C10.7838 65.8436 11.2223 65.5128 11.5846 65.1066C11.9469 64.7003 12.2258 64.2265 12.4054 63.7123L17.0156 50.5114H41.3745ZM42.4257 60.5635L66.8508 36.0783C67.7291 35.1977 68.7718 34.499 69.9194 34.0223C71.0669 33.5456 72.297 33.3001 73.5392 33.2999C74.7814 33.2997 76.0115 33.5448 77.1592 34.0212C78.307 34.4975 79.3499 35.1959 80.2284 36.0763C81.1069 36.9567 81.8038 38.0019 82.2794 39.1523C82.7549 40.3027 82.9998 41.5358 83 42.781C83.0002 44.0263 82.7557 45.2594 82.2805 46.41C81.8053 47.5605 81.1087 48.606 80.2305 49.4867L55.8012 73.9718C54.376 75.3977 52.593 76.4111 50.6405 76.9048L43.0672 78.8008C42.8603 78.8561 42.6533 78.8934 42.4464 78.9128C40.0047 79.7591 30.118 81.2941 26.6086 78.5477C24.2331 76.6891 24.7049 73.0134 25.7064 71.0055C25.8719 70.6819 25.6236 70.2588 25.2718 70.3168C22.5405 70.7442 20.1277 72.1837 17.7109 73.6316C14.4705 75.5731 11.2135 77.5147 7.16198 77.0127C3.11044 76.5107 1.08674 74.0008 0.0852306 71.8062C-0.324476 70.9101 0.830152 70.1426 1.69095 70.6155C3.63188 71.6734 6.26808 72.6733 8.44076 72.192C9.99268 71.8435 12.1819 70.292 14.6319 68.5537C19.0435 65.4298 24.3034 61.696 28.2557 63.2932C31.732 64.6913 34.2978 68.0642 32.5183 72.0593C32.3031 72.5488 32.5183 73.1545 33.0438 73.2748C34.7944 73.6772 36.2636 73.5486 37.7327 72.8392L39.504 65.7368C39.9923 63.7786 41.0021 61.9906 42.4257 60.5635Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default TextIcon;
