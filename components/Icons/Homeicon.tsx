type HomeIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const HomeIcon: React.FC<HomeIconProps> = ({ className, ...props }) => (
  <svg width="24" height="24" viewBox="0 0 73 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
    <path
      d="M0 75.0075V29.6849C0 28.3501 0.298952 27.0865 0.896857 25.8941C1.49476 24.7018 2.31862 23.7198 3.36843 22.9481L31.4474 1.68422C32.9178 0.561407 34.5968 0 36.4844 0C38.3719 0 40.0614 0.561407 41.5526 1.68422L69.6316 22.9429C70.6849 23.7146 71.5087 24.6983 72.1031 25.8941C72.701 27.0865 73 28.3501 73 29.6849V75.0075C73 76.4049 72.4803 77.6233 71.4409 78.6627C70.4016 79.7021 69.1831 80.2218 67.7857 80.2218H50.1406C48.9448 80.2218 47.9436 79.8186 47.1371 79.0121C46.3307 78.2021 45.9274 77.201 45.9274 76.0087V51.1417C45.9274 49.9494 45.5242 48.95 44.7177 48.1435C43.9078 47.3336 42.9066 46.9286 41.7143 46.9286H31.2857C30.0934 46.9286 29.094 47.3336 28.2875 48.1435C27.4775 48.95 27.0726 49.9494 27.0726 51.1417V76.0139C27.0726 77.2062 26.6693 78.2056 25.8629 79.0121C25.0564 79.8186 24.057 80.2218 22.8646 80.2218H5.21429C3.81686 80.2218 2.59845 79.7021 1.55907 78.6627C0.519689 77.6233 0 76.4049 0 75.0075Z"
      fill="currentColor"
    />
  </svg>
);

export default HomeIcon;
