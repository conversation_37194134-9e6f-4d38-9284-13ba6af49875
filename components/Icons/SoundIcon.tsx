type SoundIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const SoundIcon: React.FC<SoundIconProps> = ({ className, ...props }) => {
  return (
    <svg width="92" height="81" viewBox="0 0 92 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M79.5239 17.4932C80.155 16.8616 81.0105 16.5069 81.9024 16.5069C82.7944 16.5069 83.6499 16.8616 84.281 17.4932L84.2899 17.5022L84.3034 17.5112L84.3348 17.5426L84.4246 17.6326L84.7028 17.9428C84.9272 18.1991 85.2414 18.5633 85.6049 19.0265C89.7515 24.4266 91.9999 31.0489 92 37.8623C91.9999 44.6757 89.7515 51.298 85.6049 56.6981C85.229 57.1787 84.8338 57.6437 84.4201 58.0921L84.3303 58.182L84.3034 58.2135L84.2899 58.2225L84.2855 58.227L81.9967 55.9427L84.2855 58.2359C83.654 58.8686 82.7976 59.224 81.9047 59.224C81.0117 59.224 80.1553 58.8686 79.5239 58.2359C78.8925 57.6033 78.5377 56.7452 78.5377 55.8506C78.5377 54.9559 78.8925 54.0978 79.5239 53.4652L79.5194 53.4697L79.5104 53.4742L79.5329 53.4517L79.681 53.2898C79.8246 53.1339 80.0325 52.8836 80.3048 52.5389C83.1439 48.8254 84.8453 44.3655 85.2022 39.7014C85.5592 35.0372 84.5562 30.3696 82.3153 26.2658C81.7303 25.1858 81.0578 24.1557 80.3048 23.1857C80.0611 22.8702 79.8035 22.5656 79.5329 22.2729L79.5104 22.2505C78.8846 21.6167 78.5348 20.7605 78.5373 19.869C78.5398 18.9775 78.8945 18.1233 79.5239 17.4932ZM52.8978 1.42268C58.1171 -2.02613 65.0732 1.72845 65.0732 7.99207V72.229C65.0732 78.4972 58.1171 82.2472 52.8978 78.7984L25.9709 61.0148C25.7888 60.8931 25.575 60.8274 25.3561 60.8259H12.3415C9.0683 60.8259 5.9292 59.5232 3.61473 57.2042C1.30026 54.8852 0 51.7401 0 48.4606V31.7606C0 28.4811 1.30026 25.3359 3.61473 23.0169C5.9292 20.698 9.0683 19.3952 12.3415 19.3952H25.3561C25.5761 19.3959 25.7915 19.3318 25.9754 19.2108L52.8978 1.42268Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default SoundIcon;
