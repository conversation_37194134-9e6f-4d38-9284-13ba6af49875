type AnonIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const AnonIcon: React.FC<AnonIconProps> = ({ className, ...props }) => (
  <svg width="24" height="24" viewBox="10 8.8125 60 60" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
    <path
      d="M52.65 42.5C48 42.5 44.1 45.825 43.1 50.25C40.725 49.225 38.55 49.5 36.9 50.225C35.875 45.775 31.975 42.5 27.35 42.5C21.925 42.5 17.5 46.975 17.5 52.5C17.5 58.025 21.925 62.5 27.35 62.5C32.5 62.5 36.7 58.45 37.1 53.3C37.95 52.7 40.175 51.575 42.9 53.35C43.35 58.475 47.5 62.5 52.65 62.5C58.075 62.5 62.5 58.025 62.5 52.5C62.5 46.975 58.075 42.5 52.65 42.5ZM27.35 59.65C23.45 59.65 20.325 56.45 20.325 52.5C20.325 48.55 23.475 45.35 27.35 45.35C31.25 45.35 34.375 48.55 34.375 52.5C34.375 56.45 31.25 59.65 27.35 59.65ZM52.65 59.65C48.75 59.65 45.625 56.45 45.625 52.5C45.625 48.55 48.75 45.35 52.65 45.35C56.55 45.35 59.7 48.55 59.7 52.5C59.7 56.45 56.525 59.65 52.65 59.65ZM65 36.25H15V40H65V36.25ZM48.825 16.575C48.275 15.35 46.875 14.7 45.55 15.125L40 16.975L34.425 15.125L34.3 15.1C32.975 14.725 31.575 15.425 31.075 16.7L25 32.5H55L48.9 16.7L48.825 16.575Z"
      fill="currentColor"
    />
  </svg>
);

export default AnonIcon;
