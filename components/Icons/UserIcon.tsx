type UserIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const UserIcon: React.FC<UserIconProps> = ({ className, ...props }) => (
  <svg width="24" height="24" viewBox="0 0 73 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
    <path
      d="M37.5 41.0101C55.4183 41.0101 73.8047 52.6572 74.9956 76.0561C75.0414 76.9581 74.7272 77.8413 74.1221 78.5119C73.517 79.1825 72.6704 79.5855 71.7683 79.6325C62.3137 80.1142 18.5272 80.4096 3.23621 79.6325C2.33328 79.5867 1.48554 79.1841 0.879462 78.5134C0.273384 77.8427 -0.0413907 76.9588 0.00437482 76.0561C1.19529 52.6618 19.5817 41.0101 37.5 41.0101ZM37.5 0.110897C32.9792 0.110897 28.6436 1.90632 25.447 5.10218C22.2503 8.29805 20.4545 12.6326 20.4545 17.1522C20.4545 21.6719 22.2503 26.0064 25.447 29.2023C28.6436 32.3981 32.9792 34.1935 37.5 34.1935C42.0208 34.1935 46.3563 32.3981 49.553 29.2023C52.7497 26.0064 54.5455 21.6719 54.5455 17.1522C54.5455 12.6326 52.7497 8.29805 49.553 5.10218C46.3563 1.90632 42.0208 0.110897 37.5 0.110897Z"
      fill="currentColor"
    />
  </svg>
);

export default UserIcon;
