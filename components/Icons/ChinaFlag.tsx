type ChinaFlagProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const ChinaFlag: React.FC<ChinaFlagProps> = ({ className, ...props }) => (
  <svg width="111" height="81" viewBox="0 0 111 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
    <path
      d="M111 67.8032C111 71.0674 109.701 74.1979 107.388 76.5061C105.075 78.8142 101.938 80.1109 98.6667 80.1109H12.3333C9.06233 80.1109 5.9253 78.8142 3.61235 76.5061C1.2994 74.1979 0 71.0674 0 67.8032V12.4186C0 9.15439 1.2994 6.02388 3.61235 3.71574C5.9253 1.4076 9.06233 0.110897 12.3333 0.110897H98.6667C101.938 0.110897 105.075 1.4076 107.388 3.71574C109.701 6.02388 111 9.15439 111 12.4186V67.8032Z"
      fill="#DE2910"
    />
    <path
      d="M34.3361 12.3478L36.6054 13.4432L38.4215 11.7017L38.0792 14.194L40.2992 15.3817L37.8202 15.8247L37.3762 18.2986L36.1861 16.0832L33.6886 16.4247L35.4337 14.6124L34.3361 12.3478ZM48.7198 21.397L47.6222 23.6586L49.3673 25.474L46.8729 25.1294L45.6827 27.3478L45.2387 24.8709L42.7567 24.4278L44.9797 23.2401L44.6344 20.7509L46.4536 22.4924L48.7198 21.397ZM45.7691 33.0247L46.5954 35.4001L49.1145 35.4524L47.1072 36.9694L47.838 39.3786L45.7691 37.9417L43.7002 39.3786L44.4278 36.9694L42.4206 35.4524L44.9397 35.4001L45.7691 33.0247ZM34.3361 43.117L36.6054 44.2124L38.4215 42.4709L38.0792 44.9632L40.2992 46.1509L37.8202 46.594L37.3762 49.0678L36.1861 46.8524L33.6886 47.194L35.4337 45.3817L34.3361 43.117ZM21.5834 18.4217L24.4478 26.6401L33.1613 26.8186L26.2146 32.074L28.7398 40.4001L21.5834 35.4309L14.427 40.4001L16.9522 32.074L10.0055 26.8186L18.719 26.6401L21.5834 18.4217Z"
      fill="#FFDE02"
    />
  </svg>
);

export default ChinaFlag;
