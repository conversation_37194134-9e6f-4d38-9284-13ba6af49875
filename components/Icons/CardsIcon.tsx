type CardsIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const CardsIcon: React.FC<CardsIconProps> = ({ className, ...props }) => (
  <svg width="24" height="24" viewBox="0 0 73 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
    <path
      d="M82.7856 8.94835L77.1397 6.5917V44.5927L87.3781 19.932C89.1056 15.6396 87.1253 10.7158 82.7856 8.94835ZM0.625054 24.5191L21.5233 74.8084C22.1387 76.3385 23.1884 77.6555 24.5433 78.5976C25.8982 79.5397 27.4992 80.0657 29.1495 80.1109C30.245 80.1109 31.3826 79.9005 32.4781 79.4376L63.5305 66.6022C66.6905 65.2976 68.6287 62.1835 68.7129 59.0693C68.7551 57.9752 68.5444 56.7548 68.1652 55.6606L47.0984 5.37129C46.5049 3.8298 45.4594 2.50307 44.0988 1.56455C42.7382 0.626029 41.1257 0.119398 39.4722 0.110897C38.3768 0.110897 37.2813 0.363396 36.2279 0.742144L5.21762 13.5775C3.15628 14.4206 1.51454 16.0468 0.653336 18.0986C-0.20787 20.1504 -0.218043 22.4598 0.625054 24.5191ZM68.6708 8.52752C68.6708 6.29529 67.783 4.15449 66.2027 2.57607C64.6224 0.997646 62.479 0.110897 60.2441 0.110897H54.1347L68.6708 35.2082"
      fill="currentColor"
    />
  </svg>
);

export default CardsIcon;
