type SettingsIconProps = React.SVGProps<SVGSVGElement> & {
  className?: string;
};

const SettingsIcon: React.FC<SettingsIconProps> = ({ className, ...props }) => (
  <svg width="76" height="81" viewBox="0 0 76 81" fill="none" xmlns="http://www.w3.org/2000/svg" className={className} {...props}>
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M45.1171 1.108C43.6371 0.5 41.7571 0.5 38.0011 0.5C34.2451 0.5 32.3692 0.5 30.8852 1.108C29.9087 1.50698 29.0208 2.09513 28.2726 2.83859C27.5243 3.58205 26.9304 4.46614 26.5252 5.44C26.1492 6.332 26.0052 7.376 25.9452 8.892C25.9166 9.99 25.6094 11.0627 25.0525 12.0094C24.4955 12.9561 23.7071 13.7457 22.7612 14.304C21.7962 14.8404 20.7114 15.1246 19.6073 15.1302C18.5032 15.1358 17.4156 14.8626 16.4452 14.336C15.0932 13.624 14.1132 13.232 13.1452 13.104C11.0276 12.8277 8.8863 13.3968 7.18524 14.688C5.91324 15.656 4.97325 17.272 3.09725 20.5C1.21726 23.728 0.281261 25.34 0.0692618 26.92C-0.210737 29.024 0.365261 31.152 1.66926 32.836C2.26125 33.604 3.09725 34.248 4.38925 35.056C6.29724 36.244 7.52124 38.268 7.52124 40.5C7.52124 42.732 6.29724 44.756 4.39325 45.94C3.09725 46.752 2.26125 47.396 1.66526 48.164C1.02352 48.9968 0.552258 49.948 0.278404 50.9631C0.00455002 51.9783 -0.066519 53.0374 0.0692618 54.08C0.281261 55.656 1.21726 57.272 3.09725 60.5C4.97725 63.728 5.91324 65.34 7.18524 66.312C8.88123 67.604 11.0252 68.172 13.1452 67.896C14.1132 67.768 15.0932 67.376 16.4452 66.664C17.4161 66.1367 18.5045 65.8631 19.6093 65.8687C20.7141 65.8743 21.7997 66.1589 22.7652 66.696C24.7092 67.816 25.8612 69.876 25.9452 72.108C26.0052 73.628 26.1492 74.668 26.5252 75.56C27.3412 77.52 28.9092 79.08 30.8852 79.892C32.3652 80.5 34.2451 80.5 38.0011 80.5C41.7571 80.5 43.6371 80.5 45.1171 79.892C46.0936 79.493 46.9815 78.9049 47.7297 78.1614C48.478 77.418 49.0718 76.5339 49.4771 75.56C49.8531 74.668 49.9971 73.628 50.0571 72.108C50.1371 69.876 51.2931 67.812 53.2411 66.696C54.2061 66.1596 55.2909 65.8754 56.395 65.8698C57.499 65.8642 58.5867 66.1374 59.5571 66.664C60.9091 67.376 61.8891 67.768 62.8571 67.896C64.9771 68.176 67.121 67.604 68.817 66.312C70.089 65.344 71.029 63.728 72.905 60.5C74.785 57.272 75.721 55.66 75.933 54.08C76.0682 53.0371 75.9965 51.9778 75.722 50.9627C75.4474 49.9476 74.9754 48.9965 74.333 48.164C73.741 47.396 72.905 46.752 71.613 45.944C69.705 44.756 68.481 42.732 68.481 40.5C68.481 38.268 69.705 36.244 71.609 35.06C72.905 34.248 73.741 33.604 74.337 32.836C74.9787 32.0032 75.45 31.052 75.7239 30.0369C75.9977 29.0217 76.0688 27.9626 75.933 26.92C75.721 25.344 74.785 23.728 72.905 20.5C71.025 17.272 70.089 15.66 68.817 14.688C67.116 13.3968 64.9747 12.8277 62.8571 13.104C61.8891 13.232 60.9091 13.624 59.5571 14.336C58.5862 14.8633 57.4978 15.1369 56.393 15.1313C55.2881 15.1257 54.2026 14.8411 53.2371 14.304C52.2919 13.7452 51.5043 12.9554 50.948 12.0088C50.3918 11.0621 50.0852 9.98962 50.0571 8.892C49.9971 7.372 49.8531 6.332 49.4771 5.44C49.0718 4.46614 48.478 3.58205 47.7297 2.83859C46.9815 2.09513 46.0936 1.50698 45.1171 1.108ZM38.0011 52.5C44.6811 52.5 50.0931 47.128 50.0931 40.5C50.0931 33.872 44.6771 28.5 38.0011 28.5C31.3252 28.5 25.9092 33.872 25.9092 40.5C25.9092 47.128 31.3252 52.5 38.0011 52.5Z"
      fill="currentColor"
    />
  </svg>
);

export default SettingsIcon;
