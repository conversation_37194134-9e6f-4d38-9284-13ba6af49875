echo "
[36m ██╗     ███████╗██╗  ██╗██╗ ██████╗ ██████╗ ███╗   ██╗    ██╗  ██╗
[36m ██║     ██╔════╝╚██╗██╔╝██║██╔════╝██╔═══██╗████╗  ██║    ╚██╗██╔╝
[36m ██║     █████╗   ╚███╔╝ ██║██║     ██║   ██║██╔██╗ ██║     ╚███╔╝ 
[36m ██║     ██╔══╝   ██╔██╗ ██║██║     ██║   ██║██║╚██╗██║     ██╔██╗ 
[36m ███████╗███████╗██╔╝ ██╗██║╚██████╗╚██████╔╝██║ ╚████║    ██╔╝ ██╗
[36m ╚══════╝╚══════╝╚═╝  ╚═╝╚═╝ ╚═════╝ ╚═════╝ ╚═╝  ╚═══╝    ╚═╝  ╚═╝
[0m
[36m               R U N N I N G   T E S T S...[0m
----------------------------------------------------------
"

pnpm test || {
  echo ""
  echo "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  echo "!!! TESTS FAILED! COMMIT ABORTED.       !!!"
  echo "!!! Please fix the tests and try again. !!!"
  echo "!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!"
  echo ""
  exit 1
}

echo "[36m Tests passed! Proceeding with commit. [0m"
exit 0
