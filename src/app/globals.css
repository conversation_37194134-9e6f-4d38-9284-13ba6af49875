@import "tailwindcss";

@theme {
  --color-theme-bg-light: #f8f8f8;
  --color-theme-bg-dark: #1e272e;
  --color-theme-fg-light: #ffffff;
  --color-theme-fg-dark: #2d3748;
  --color-theme-text-light: #2c3e50;
  --color-theme-text-dark: #e0f2f7;

  --color-secondary: #059aad;
  --color-warning: #dba802;
  --color-error: #ef5350;
  --color-info: #016999;
}

@custom-variant dark (&:where(.dark, .dark *));

@keyframes blink-success {
  0%,
  100% {
    background-color: theme(colors.info);
  }
  50% {
    background-color: transparent;
  }
}

@keyframes blink-error {
  0%,
  100% {
    background-color: theme(colors.error);
  }
  50% {
    background-color: transparent;
  }
}

html {
  scroll-behavior: smooth;
}

.blink-success {
  animation: blink-success 0.5s infinite;
}

.blink-error {
  animation: blink-error 0.5s infinite;
}

body {
  font-family: system-ui, -apple-system, sans-serif;
  transition: background-color 0.3s ease, color 0.3s ease;
  background-color: theme(colors.theme-bg-light);
  color: theme(colors.theme-text-light);
}

.dark body,
html.dark body,
:root.dark body {
  background-color: theme(colors.theme-bg-dark);
  color: theme(colors.theme-text-dark);
}
